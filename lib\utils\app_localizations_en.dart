// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'Market';

  @override
  String get dashboardTitle => 'Dashboard';

  @override
  String get productsTitle => 'Products';

  @override
  String get customersTitle => 'Customers';

  @override
  String get salesTitle => 'Sales';

  @override
  String get reportsTitle => 'Reports';

  @override
  String get settingsTitle => 'Settings';

  @override
  String get confirmExitTitle => 'Confirm Exit';

  @override
  String get confirmExitMessage => 'Do you want to exit the app?';

  @override
  String get yesButton => 'Yes';

  @override
  String get noButton => 'No';

  @override
  String get okButton => 'OK';

  @override
  String get add => 'Add';

  @override
  String get edit => 'Edit';

  @override
  String get delete => 'Delete';

  @override
  String get save => 'Save';

  @override
  String get cancel => 'Cancel';

  @override
  String get search => 'Search';

  @override
  String get name => 'Name';

  @override
  String get description => 'Description';

  @override
  String get category => 'Category';

  @override
  String get price => 'Price';

  @override
  String get quantity => 'Quantity';

  @override
  String get total => 'Total';

  @override
  String get date => 'Date';

  @override
  String get phone => 'Phone';

  @override
  String get address => 'Address';

  @override
  String get email => 'Email';
}
