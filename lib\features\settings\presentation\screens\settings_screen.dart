import 'package:flutter/material.dart';
import 'package:market/shared_widgets/wrappers.dart';
import 'package:market/utils/app_localizations.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return SecondaryScreenWrapper(
      title: AppLocalizations.of(context).settingsTitle,
      child: const Center(child: Text('شاشة الإعدادات')),
    );
  }
}
