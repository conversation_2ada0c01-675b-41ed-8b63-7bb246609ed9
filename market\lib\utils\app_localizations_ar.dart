// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get appTitle => 'ماركت';

  @override
  String get dashboard => 'لوحة التحكم';

  @override
  String get products => 'المنتجات';

  @override
  String get sales => 'المبيعات';

  @override
  String get customers => 'العملاء';

  @override
  String get suppliers => 'الموردين';

  @override
  String get expenses => 'المصروفات';

  @override
  String get reports => 'التقارير';

  @override
  String get settings => 'الإعدادات';

  @override
  String get add => 'إضافة';

  @override
  String get edit => 'تعديل';

  @override
  String get delete => 'حذف';

  @override
  String get save => 'حفظ';

  @override
  String get cancel => 'إلغاء';

  @override
  String get search => 'بحث';

  @override
  String get name => 'الاسم';

  @override
  String get description => 'الوصف';

  @override
  String get category => 'الفئة';

  @override
  String get price => 'السعر';

  @override
  String get quantity => 'الكمية';

  @override
  String get total => 'الإجمالي';

  @override
  String get date => 'التاريخ';

  @override
  String get phone => 'الهاتف';

  @override
  String get address => 'العنوان';

  @override
  String get email => 'البريد الإلكتروني';
}
