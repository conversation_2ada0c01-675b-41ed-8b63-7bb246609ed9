// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get appTitle => 'ماركت';

  @override
  String get dashboardTitle => 'لوحة القيادة';

  @override
  String get productsTitle => 'المنتجات';

  @override
  String get customersTitle => 'العملاء';

  @override
  String get salesTitle => 'المبيعات';

  @override
  String get reportsTitle => 'التقارير';

  @override
  String get settingsTitle => 'الإعدادات';

  @override
  String get confirmExitTitle => 'تأكيد الخروج';

  @override
  String get confirmExitMessage => 'هل تريد الخروج من التطبيق؟';

  @override
  String get yesButton => 'نعم';

  @override
  String get noButton => 'لا';

  @override
  String get okButton => 'موافق';

  @override
  String get add => 'إضافة';

  @override
  String get edit => 'تعديل';

  @override
  String get delete => 'حذف';

  @override
  String get save => 'حفظ';

  @override
  String get cancel => 'إلغاء';

  @override
  String get search => 'بحث';

  @override
  String get name => 'الاسم';

  @override
  String get description => 'الوصف';

  @override
  String get category => 'الفئة';

  @override
  String get price => 'السعر';

  @override
  String get quantity => 'الكمية';

  @override
  String get total => 'الإجمالي';

  @override
  String get date => 'التاريخ';

  @override
  String get phone => 'الهاتف';

  @override
  String get address => 'العنوان';

  @override
  String get email => 'البريد الإلكتروني';
}
