import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'package:market/core/di/service_locator.dart';
import 'package:market/core/navigation/app_router.dart';
import 'package:market/core/database/database_helper.dart';
import 'package:market/core/navigation/navigation_manager.dart';
import 'package:market/utils/app_localizations.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  setupServiceLocator();
  await getIt<DatabaseHelper>().database; // تهيئة قاعدة البيانات
  runApp(
    MultiProvider(
      providers: const [], // سيتم إضافة Providers هنا في المراحل القادمة
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    // تم تغليف MaterialApp.router بـ BackButtonHandler
    return BackButtonHandler(
      child: MaterialApp.router(
        title: 'ماركت',
        theme: ThemeData(
          primarySwatch: Colors.blue,
          useMaterial3: true,
          fontFamily:
              'Cairo', // تأكد من وجود هذا الخط أو إزالته إذا لم يكن لديك
        ),
        routerConfig: AppRouter.router,
        localizationsDelegates: const [
          AppLocalizations.delegate,
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: const [
          Locale('en', ''), // الإنجليزية
          Locale('ar', ''), // العربية
        ],
        // إصلاح: استخدام أسماء متغيرات واضحة بدلاً من الاختصارات المفرطة
        localeResolutionCallback: (locale, supportedLocales) {
          if (locale != null) {
            for (var supportedLocale in supportedLocales) {
              // استخدام اسم كامل للمتغير
              if (supportedLocale.languageCode == locale.languageCode) {
                return supportedLocale;
              }
            }
          }
          return supportedLocales
              .first; // الافتراضي إلى أول لغة مدعومة (الإنجليزية)
        },
      ),
    );
  }
}
