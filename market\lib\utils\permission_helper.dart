import 'package:permission_handler/permission_handler.dart';

class PermissionHelper {
  // طلب إذن الوصول للتخزين
  static Future<bool> requestStoragePermission() async {
    final status = await Permission.storage.request();
    return status.isGranted;
  }
  
  // طلب إذن الكاميرا (للباركود)
  static Future<bool> requestCameraPermission() async {
    final status = await Permission.camera.request();
    return status.isGranted;
  }
  
  // التحقق من إذن التخزين
  static Future<bool> hasStoragePermission() async {
    final status = await Permission.storage.status;
    return status.isGranted;
  }
  
  // التحقق من إذن الكاميرا
  static Future<bool> hasCameraPermission() async {
    final status = await Permission.camera.status;
    return status.isGranted;
  }
  
  // طلب جميع الأذونات المطلوبة
  static Future<Map<Permission, PermissionStatus>> requestAllPermissions() async {
    return await [
      Permission.storage,
      Permission.camera,
    ].request();
  }
  
  // التحقق من حالة إذن معين
  static Future<bool> isPermissionGranted(Permission permission) async {
    final status = await permission.status;
    return status.isGranted;
  }
  
  // فتح إعدادات التطبيق
  static Future<bool> openAppSettings() async {
    return await openAppSettings();
  }
}
