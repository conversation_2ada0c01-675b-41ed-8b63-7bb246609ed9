import 'package:go_router/go_router.dart';
import 'package:market/features/dashboard/presentation/screens/dashboard_screen.dart';
import 'package:market/features/products/presentation/screens/products_screen.dart';
import 'package:market/features/customers/presentation/screens/customers_screen.dart';
import 'package:market/features/sales/presentation/screens/sales_screen.dart';
import 'package:market/features/reports/presentation/screens/reports_screen.dart';
import 'package:market/features/settings/presentation/screens/settings_screen.dart';

class AppRouter {
  static final GoRouter router = GoRouter(
    initialLocation: '/',
    routes: <RouteBase>[
      GoRoute(path: '/', builder: (context, state) => const DashboardScreen()),
      GoRoute(
        path: '/products',
        builder: (context, state) => const ProductsScreen(),
      ),
      GoRoute(
        path: '/customers',
        builder: (context, state) => const CustomersScreen(),
      ),
      GoRoute(path: '/sales', builder: (context, state) => const SalesScreen()),
      GoRoute(
        path: '/reports',
        builder: (context, state) => const ReportsScreen(),
      ),
      GoRoute(
        path: '/settings',
        builder: (context, state) => const SettingsScreen(),
      ),
    ],
  );
}
