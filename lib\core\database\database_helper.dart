import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;

  factory DatabaseHelper() {
    return _instance;
  }

  DatabaseHelper._internal();

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDb();
    return _database!;
  }

  Future<Database> _initDb() async {
    // تحديد مسار قاعدة البيانات
    String path = join(await getDatabasesPath(), 'market.db');
    // فتح قاعدة البيانات أو إنشاؤها
    return await openDatabase(
      path,
      version: 1, // رقم إصدار قاعدة البيانات
      onCreate: _onCreate, // دالة تُستدعى عند إنشاء قاعدة البيانات لأول مرة
    );
  }

  // دالة لإنشاء الجداول
  Future _onCreate(Database db, int version) async {
    // إنشاء جدول المنتجات (products)
    await db.execute('''
      CREATE TABLE products (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        category TEXT,
        unit TEXT,
        purchasePrice REAL,
        salePrice REAL,
        minStockQuantity INTEGER,
        barcode TEXT UNIQUE,
        warehouseQuantity INTEGER DEFAULT 0,
        storeQuantity INTEGER DEFAULT 0
      )
    ''');
    // هنا سيتم إضافة إنشاء الجداول الأخرى (مثل customers, suppliers, sales, etc.) لاحقًا
  }
}
