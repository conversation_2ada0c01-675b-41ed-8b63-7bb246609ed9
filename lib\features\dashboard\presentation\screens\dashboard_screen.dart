import 'package:flutter/material.dart';
import 'package:market/shared_widgets/wrappers.dart';
import 'package:market/utils/app_localizations.dart';

class DashboardScreen extends StatelessWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return MainScreenWrapper(
      title: AppLocalizations.of(context).dashboardTitle,
      needsDrawer: true,
      showBottomNav: true,
      child: const Center(child: Text('شاشة لوحة القيادة')),
    );
  }
}
