import 'package:intl/intl.dart';

class DateHelper {
  // تنسيقات التاريخ المختلفة
  static final DateFormat _dateFormat = DateFormat('yyyy-MM-dd');
  static final DateFormat _dateTimeFormat = DateFormat('yyyy-MM-dd HH:mm:ss');
  static final DateFormat _displayDateFormat = DateFormat('dd/MM/yyyy');
  static final DateFormat _displayDateTimeFormat = DateFormat('dd/MM/yyyy HH:mm');
  
  // تحويل التاريخ إلى نص للعرض
  static String formatDateForDisplay(DateTime date) {
    return _displayDateFormat.format(date);
  }
  
  // تحويل التاريخ والوقت إلى نص للعرض
  static String formatDateTimeForDisplay(DateTime dateTime) {
    return _displayDateTimeFormat.format(dateTime);
  }
  
  // تحويل التاريخ إلى نص لقاعدة البيانات
  static String formatDateForDatabase(DateTime date) {
    return _dateFormat.format(date);
  }
  
  // تحويل التاريخ والوقت إلى نص لقاعدة البيانات
  static String formatDateTimeForDatabase(DateTime dateTime) {
    return _dateTimeFormat.format(dateTime);
  }
  
  // تحويل النص إلى تاريخ
  static DateTime? parseDateFromString(String dateString) {
    try {
      return _dateFormat.parse(dateString);
    } catch (e) {
      return null;
    }
  }
  
  // تحويل النص إلى تاريخ ووقت
  static DateTime? parseDateTimeFromString(String dateTimeString) {
    try {
      return _dateTimeFormat.parse(dateTimeString);
    } catch (e) {
      return null;
    }
  }
  
  // الحصول على بداية اليوم
  static DateTime startOfDay(DateTime date) {
    return DateTime(date.year, date.month, date.day);
  }
  
  // الحصول على نهاية اليوم
  static DateTime endOfDay(DateTime date) {
    return DateTime(date.year, date.month, date.day, 23, 59, 59);
  }
  
  // الحصول على بداية الشهر
  static DateTime startOfMonth(DateTime date) {
    return DateTime(date.year, date.month, 1);
  }
  
  // الحصول على نهاية الشهر
  static DateTime endOfMonth(DateTime date) {
    return DateTime(date.year, date.month + 1, 0, 23, 59, 59);
  }
}
