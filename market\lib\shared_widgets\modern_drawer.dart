import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:market/utils/app_localizations.dart';

class ModernDrawer extends StatelessWidget {
  const ModernDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: <Widget>[
          DrawerHeader(
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor,
            ),
            child: const Text(
              'ماركت',
              style: TextStyle(
                color: Colors.white,
                fontSize: 24,
              ),
            ),
          ),
          _buildItem(
            context,
            Icons.dashboard,
            AppLocalizations.of(context)!.dashboardTitle,
            '/',
          ),
          _buildItem(
            context,
            Icons.inventory,
            AppLocalizations.of(context)!.productsTitle,
            '/products',
          ),
          _buildItem(
            context,
            Icons.people,
            AppLocalizations.of(context)!.customersTitle,
            '/customers',
          ),
          _buildItem(
            context,
            Icons.shopping_cart,
            AppLocalizations.of(context)!.salesTitle,
            '/sales',
          ),
          _buildItem(
            context,
            Icons.bar_chart,
            AppLocalizations.of(context)!.reportsTitle,
            '/reports',
          ),
          _buildItem(
            context,
            Icons.settings,
            AppLocalizations.of(context)!.settingsTitle,
            '/settings',
          ),
        ],
      ),
    );
  }

  ListTile _buildItem(
    BuildContext context,
    IconData icon,
    String title,
    String path,
  ) {
    return ListTile(
      leading: Icon(icon),
      title: Text(title),
      onTap: () {
        context.go(path);
        Navigator.pop(context);
      },
    );
  }
}
