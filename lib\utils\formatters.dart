import 'package:intl/intl.dart';

class Formatters {
  // منسق العملة
  static final NumberFormat _currencyFormat = NumberFormat.currency(
    locale: 'ar_SA',
    symbol: 'ر.س',
    decimalDigits: 2,
  );
  
  // منسق الأرقام
  static final NumberFormat _numberFormat = NumberFormat('#,##0.##', 'ar_SA');
  
  // تنسيق العملة
  static String formatCurrency(double amount) {
    return _currencyFormat.format(amount);
  }
  
  // تنسيق الأرقام
  static String formatNumber(double number) {
    return _numberFormat.format(number);
  }
  
  // تنسيق الكمية
  static String formatQuantity(int quantity) {
    return NumberFormat('#,##0', 'ar_SA').format(quantity);
  }
  
  // تحويل النص إلى رقم
  static double? parseDouble(String text) {
    try {
      // إزالة الفواصل والرموز
      String cleanText = text.replaceAll(',', '').replaceAll('ر.س', '').trim();
      return double.parse(cleanText);
    } catch (e) {
      return null;
    }
  }
  
  // تحويل النص إلى عدد صحيح
  static int? parseInt(String text) {
    try {
      String cleanText = text.replaceAll(',', '').trim();
      return int.parse(cleanText);
    } catch (e) {
      return null;
    }
  }
  
  // تنسيق النسبة المئوية
  static String formatPercentage(double percentage) {
    return '${percentage.toStringAsFixed(1)}%';
  }
  
  // تقصير النص
  static String truncateText(String text, int maxLength) {
    if (text.length <= maxLength) {
      return text;
    }
    return '${text.substring(0, maxLength)}...';
  }
  
  // تنسيق رقم الهاتف
  static String formatPhoneNumber(String phoneNumber) {
    // إزالة جميع الرموز غير الرقمية
    String cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
    
    // إضافة التنسيق للأرقام السعودية
    if (cleanNumber.startsWith('966')) {
      cleanNumber = cleanNumber.substring(3);
    } else if (cleanNumber.startsWith('0')) {
      cleanNumber = cleanNumber.substring(1);
    }
    
    if (cleanNumber.length == 9) {
      return '${cleanNumber.substring(0, 3)} ${cleanNumber.substring(3, 6)} ${cleanNumber.substring(6)}';
    }
    
    return phoneNumber; // إرجاع الرقم كما هو إذا لم يكن بالتنسيق المتوقع
  }
}
