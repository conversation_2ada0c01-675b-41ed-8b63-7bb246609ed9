import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:market/utils/app_localizations.dart';

class BackButtonHandler extends StatelessWidget {
  final Widget child;

  const BackButtonHandler({super.key, required this.child});

  Future<bool> _onWillPop(BuildContext context) async {
    final GoRouter router = GoRouter.of(context);
    final String currentLocation = GoRouterState.of(context).uri.toString();

    if (router.canPop()) {
      router.pop();
      return false;
    }

    if (currentLocation == '/') {
      return await showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: Text(AppLocalizations.of(context)!.confirmExitTitle),
              content: Text(AppLocalizations.of(context)!.confirmExitMessage),
              actions: <Widget>[
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: Text(AppLocalizations.of(context)!.noButton),
                ),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  child: Text(AppLocalizations.of(context)!.yesButton),
                ),
              ],
            ),
          ) ??
          false;
    }

    return true;
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) return;
        await _onWillPop(context);
      },
      child: child,
    );
  }
}
