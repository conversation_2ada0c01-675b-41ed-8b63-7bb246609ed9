import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:market/utils/app_localizations.dart';

class ConditionalBottomNav extends StatelessWidget {
  const ConditionalBottomNav({super.key});

  int _getCurrentIndex(BuildContext context) {
    final String location = GoRouterState.of(context).uri.toString();
    if (location == '/') return 0;
    if (location.startsWith('/products')) return 1;
    if (location.startsWith('/customers')) return 2;
    if (location.startsWith('/sales')) return 3;
    if (location.startsWith('/reports')) return 4;
    return 0;
  }

  void _onItemTapped(BuildContext context, int index) {
    switch (index) {
      case 0:
        context.go('/');
        break;
      case 1:
        context.go('/products');
        break;
      case 2:
        context.go('/customers');
        break;
      case 3:
        context.go('/sales');
        break;
      case 4:
        context.go('/reports');
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return BottomNavigationBar(
      currentIndex: _getCurrentIndex(context),
      onTap: (index) => _onItemTapped(context, index),
      type: BottomNavigationBarType.fixed,
      selectedItemColor: Theme.of(context).primaryColor,
      unselectedItemColor: Colors.grey,
      items: <BottomNavigationBarItem>[
        BottomNavigationBarItem(
          icon: const Icon(Icons.dashboard),
          label: AppLocalizations.of(context).dashboardTitle,
        ),
        BottomNavigationBarItem(
          icon: const Icon(Icons.inventory),
          label: AppLocalizations.of(context).productsTitle,
        ),
        BottomNavigationBarItem(
          icon: const Icon(Icons.people),
          label: AppLocalizations.of(context).customersTitle,
        ),
        BottomNavigationBarItem(
          icon: const Icon(Icons.shopping_cart),
          label: AppLocalizations.of(context).salesTitle,
        ),
        BottomNavigationBarItem(
          icon: const Icon(Icons.bar_chart),
          label: AppLocalizations.of(context).reportsTitle,
        ),
      ],
    );
  }
}
