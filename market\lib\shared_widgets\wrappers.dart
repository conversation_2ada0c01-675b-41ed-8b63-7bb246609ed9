import 'package:flutter/material.dart';
import 'package:market/shared_widgets/custom_app_bar.dart';
import 'package:market/shared_widgets/modern_drawer.dart';
import 'package:market/shared_widgets/conditional_bottom_nav.dart';

class MainScreenWrapper extends StatelessWidget {
  final String title;
  final Widget child;
  final bool showBottomNav;
  final bool needsDrawer;
  final List<Widget>? actions;

  const MainScreenWrapper({
    super.key,
    required this.title,
    required this.child,
    this.showBottomNav = true,
    this.needsDrawer = true,
    this.actions,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: title,
        showBackButton: !needsDrawer,
        actions: actions,
      ),
      drawer: needsDrawer ? const ModernDrawer() : null,
      body: child,
      bottomNavigationBar: showBottomNav ? const ConditionalBottomNav() : null,
    );
  }
}

class SecondaryScreenWrapper extends StatelessWidget {
  final String title;
  final Widget child;
  final VoidCallback? onBackPressed;
  final List<Widget>? actions;

  const SecondaryScreenWrapper({
    super.key,
    required this.title,
    required this.child,
    this.onBackPressed,
    this.actions,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: title,
        showBackButton: true,
        onBackPressed: onBackPressed,
        actions: actions,
      ),
      body: child,
    );
  }
}
